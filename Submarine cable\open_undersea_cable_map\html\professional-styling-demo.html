<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Submarine Cable Map - Visual Styling Demo</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0; 
            padding: 0; 
            background-color: #f8f9fa;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 8px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .demo-controls {
            background: white;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .control-section {
            display: inline-block;
            margin: 0 20px;
            vertical-align: top;
        }
        
        .control-section h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 8px 8px 0;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .demo-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .demo-button.secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        }
        
        .demo-button.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        #map { 
            height: calc(100vh - 160px);
            width: 100%;
        }
        
        .info {
            padding: 12px 16px;
            font: 14px/18px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.2);
            max-width: 300px;
        }
        .info h4 {
            margin: 0 0 8px;
            color: #2c3e50;
            font-weight: 600;
        }
        
        /* Professional interactive cable label styles */
        .interactive-cable-label {
            background: none !important;
            border: none !important;
            box-shadow: none !important;
        }
        
        .interactive-cable-label-text {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(52, 73, 94, 0.3);
            border-radius: 8px;
            padding: 10px 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            text-align: center;
            white-space: nowrap;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            pointer-events: none;
            user-select: none;
            position: relative;
            line-height: 1.3;
            backdrop-filter: blur(10px);
        }
        
        .interactive-cable-label-text::before {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 8px solid rgba(255, 255, 255, 0.95);
        }
        
        .feature-showcase {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            font-size: 12px;
            z-index: 1000;
            max-width: 250px;
        }
        
        .feature-showcase h4 {
            margin: 0 0 12px 0;
            color: #2c3e50;
            font-size: 14px;
            font-weight: 600;
        }
        
        .feature-item {
            margin: 8px 0;
            display: flex;
            align-items: center;
            color: #495057;
        }
        
        .feature-icon {
            margin-right: 8px;
            font-size: 14px;
        }
        
        .color-sample {
            display: inline-flex;
            gap: 2px;
            margin-left: 8px;
        }
        
        .color-dot {
            width: 12px;
            height: 3px;
            border-radius: 1px;
        }
        
        @media (max-width: 768px) {
            .header h1 { font-size: 24px; }
            .header p { font-size: 14px; }
            .demo-controls { padding: 15px; }
            .control-section { margin: 0 10px; }
            .feature-showcase { 
                position: relative; 
                top: auto; 
                right: auto; 
                margin: 10px; 
                max-width: none;
            }
            .interactive-cable-label-text {
                font-size: 12px;
                padding: 8px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌊 Professional Submarine Cable Map</h1>
        <p>Enhanced Visual Styling & Interactive Cable Labeling System</p>
    </div>
    
    <div class="demo-controls">
        <div class="control-section">
            <h3>🎨 Visual Features</h3>
            <button class="demo-button" onclick="demonstrateColors()">Show Color Palette</button>
            <button class="demo-button secondary" onclick="toggleProfessionalMode()">Toggle Pro Mode</button>
        </div>
        <div class="control-section">
            <h3>🏷️ Interactive Labels</h3>
            <button class="demo-button" onclick="demonstrateLabeling()">Demo Labeling</button>
            <button class="demo-button secondary" onclick="clearAllLabels()">Clear Labels</button>
        </div>
        <div class="control-section">
            <h3>🌍 Navigation</h3>
            <button class="demo-button success" onclick="focusAfrica()">Focus Africa</button>
            <button class="demo-button success" onclick="focusEurope()">Focus Europe</button>
        </div>
    </div>
    
    <div id="map"></div>
    
    <div class="feature-showcase">
        <h4>✨ Professional Features</h4>
        <div class="feature-item">
            <span class="feature-icon">🎨</span>
            <span>Professional Color Palette</span>
            <div class="color-sample">
                <div class="color-dot" style="background: #2E86AB;"></div>
                <div class="color-dot" style="background: #A23B72;"></div>
                <div class="color-dot" style="background: #F18F01;"></div>
                <div class="color-dot" style="background: #C73E1D;"></div>
                <div class="color-dot" style="background: #6C757D;"></div>
            </div>
        </div>
        <div class="feature-item">
            <span class="feature-icon">🏷️</span>
            <span>Click-to-Label System</span>
        </div>
        <div class="feature-item">
            <span class="feature-icon">👆</span>
            <span>Professional Hover Effects</span>
        </div>
        <div class="feature-item">
            <span class="feature-icon">📱</span>
            <span>Responsive Design</span>
        </div>
        <div class="feature-item">
            <span class="feature-icon">⚡</span>
            <span>Optimized Performance</span>
        </div>
        <div class="feature-item">
            <span class="feature-icon">🎯</span>
            <span>Accessibility Focused</span>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize map with professional settings
        const map = L.map('map', {
            worldCopyJump: false,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0
        }).setView([20, 0], 3);

        // Add CartoDB Positron tile layer
        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        // Create layer groups
        const cableLayer = L.layerGroup().addTo(map);
        
        // Professional color palette (same as main implementation)
        const professionalCableColors = [
            '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6C757D', '#495057',
            '#4A90A4', '#8E44AD', '#E67E22', '#D35400', '#7F8C8D', '#2C3E50',
            '#3498DB', '#9B59B6', '#F39C12', '#E74C3C', '#95A5A6', '#34495E',
            '#5DADE2', '#BB8FCE', '#F7DC6F', '#F1948A', '#AEB6BF', '#566573',
            '#85C1E9', '#D2B4DE', '#FCF3CF', '#FADBD8', '#D5DBDB', '#85929E'
        ];

        // Global variables
        let currentCableLabel = null;
        let currentHighlightedCable = null;
        let professionalMode = true;
        let cableData = null;

        // Professional color function
        function getProfessionalCableColor(cableId, index) {
            let hash = 0;
            for (let i = 0; i < cableId.length; i++) {
                const char = cableId.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            const colorIndex = (Math.abs(hash) + index) % professionalCableColors.length;
            return professionalCableColors[colorIndex];
        }

        // Interactive label creation
        function createInteractiveCableLabel(feature, clickLatLng) {
            if (currentCableLabel) {
                map.removeLayer(currentCableLabel);
                currentCableLabel = null;
            }

            const cableName = feature.properties.name || feature.properties.id;
            if (!cableName) return;

            const labelIcon = L.divIcon({
                className: 'interactive-cable-label',
                html: `<div class="interactive-cable-label-text">${cableName}</div>`,
                iconSize: [null, null],
                iconAnchor: [0, 0]
            });

            currentCableLabel = L.marker(clickLatLng, {
                icon: labelIcon,
                interactive: false,
                zIndexOffset: 2000
            }).addTo(map);
        }

        // Remove cable label
        function removeCableLabel() {
            if (currentCableLabel) {
                map.removeLayer(currentCableLabel);
                currentCableLabel = null;
            }
            if (currentHighlightedCable) {
                currentHighlightedCable.setStyle({
                    weight: 2,
                    opacity: 0.8
                });
                currentHighlightedCable = null;
            }
        }

        // Add info control
        const info = L.control();
        info.onAdd = function(map) {
            this._div = L.DomUtil.create('div', 'info');
            this.update();
            return this._div;
        };
        info.update = function(props) {
            this._div.innerHTML = '<h4>🌊 Professional Cable Map</h4>' +
                (props ?
                    '<b>' + props.name + '</b><br/>' +
                    (props.rfs ? 'Ready for Service: ' + props.rfs + '<br/>' : '') +
                    (props.length ? 'Length: ' + props.length + ' km<br/>' : '') +
                    (props.owners ? 'Owners: ' + props.owners + '<br/>' : '') +
                    '<br/><em style="color: #7f8c8d;">Click cable to show name label</em>'
                    : 'Hover over a cable for details<br/><em style="color: #7f8c8d;">Click any cable to show its name</em>');
        };
        info.addTo(map);

        // Load and display cables (demo with first 30 cables for performance)
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                cableData = data;
                
                // Use first 30 cables for demo
                const demoFeatures = data.features.slice(0, 30);
                const demoData = { ...data, features: demoFeatures };

                L.geoJSON(demoData, {
                    style: function(feature) {
                        const cableIndex = demoFeatures.indexOf(feature);
                        const professionalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                        
                        return {
                            color: professionalColor,
                            weight: 2,
                            opacity: 0.8,
                            lineCap: 'round',
                            lineJoin: 'round',
                            dashArray: feature.properties.is_planned ? '8, 8' : null
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const cableIndex = demoFeatures.indexOf(feature);
                        const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                        
                        layer.on({
                            mouseover: function(e) {
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1,
                                    color: originalColor
                                });
                                info.update(feature.properties);
                            },
                            mouseout: function(e) {
                                if (currentHighlightedCable !== layer) {
                                    layer.setStyle({
                                        weight: 2,
                                        opacity: 0.8,
                                        color: originalColor
                                    });
                                }
                                info.update();
                            },
                            click: function(e) {
                                removeCableLabel();
                                
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1,
                                    color: originalColor
                                });
                                currentHighlightedCable = layer;
                                
                                createInteractiveCableLabel(feature, e.latlng);
                                L.DomEvent.stopPropagation(e);
                            }
                        });

                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<b style="color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</b><br/>';
                        popupContent += '<span style="color: ' + originalColor + '; font-size: 16px; margin-right: 5px;">●</span>';
                        popupContent += '<span style="color: #7f8c8d; font-size: 12px;">Professional Color</span><br/>';
                        
                        if (feature.properties.rfs) {
                            popupContent += '<span style="color: #34495e; font-size: 12px;">Ready for Service: ' + feature.properties.rfs + '</span><br/>';
                        }
                        if (feature.properties.length) {
                            popupContent += '<span style="color: #34495e; font-size: 12px;">Length: ' + feature.properties.length + ' km</span><br/>';
                        }
                        popupContent += '<br/><em style="color: #95a5a6; font-size: 11px;">Click cable to show name label</em>';
                        popupContent += '</div>';
                        
                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);
            });

        // Demo functions
        function demonstrateColors() {
            alert('🎨 Professional Color Palette Features:\n\n' +
                  '• 30+ carefully selected muted colors\n' +
                  '• Inspired by submarinecablemap.com design\n' +
                  '• Optimized for CartoDB Positron background\n' +
                  '• Consistent color assignment per cable\n' +
                  '• Accessibility-focused contrast ratios\n' +
                  '• Dashed lines for planned cables');
        }

        function demonstrateLabeling() {
            alert('🏷️ Interactive Cable Labeling:\n\n' +
                  '• Click any cable to show its name\n' +
                  '• Professional typography and styling\n' +
                  '• Smart positioning with pointer arrow\n' +
                  '• Only one label visible at a time\n' +
                  '• Click elsewhere to remove label\n' +
                  '• Responsive design for all devices');
        }

        function toggleProfessionalMode() {
            professionalMode = !professionalMode;
            alert(professionalMode ? 
                '✅ Professional Mode: ON\n\nFeatures active:\n• Muted color palette\n• Professional typography\n• Enhanced interactions' :
                '❌ Professional Mode: OFF\n\nBasic styling active');
        }

        function clearAllLabels() {
            removeCableLabel();
            alert('🧹 All cable labels cleared!');
        }

        function focusAfrica() {
            map.setView([0, 20], 4);
        }

        function focusEurope() {
            map.setView([54, 15], 4);
        }

        // Map click handler to remove labels
        map.on('click', function(e) {
            removeCableLabel();
        });

        // Add scale control
        L.control.scale().addTo(map);
    </script>
</body>
</html>
