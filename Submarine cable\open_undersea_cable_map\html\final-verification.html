<!DOCTYPE html>
<html>
<head>
    <title>Final African Cables Verification</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { margin: 0; font-family: Arial, sans-serif; }
        .header { padding: 15px; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; }
        .status { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        #map { height: 70vh; width: 100%; }
        .btn { padding: 8px 16px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background-color: #0056b3; }
        .cable-list { max-height: 200px; overflow-y: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h2>🔍 Final African Cables Verification</h2>
        <p>Testing the updated filtering logic with African cable exceptions.</p>
        
        <div id="status" class="status info">
            <strong>Status:</strong> Loading and testing...
        </div>
        
        <div class="btn-group">
            <button class="btn" onclick="focusOnAfrica()">🌍 Focus on Africa</button>
            <button class="btn" onclick="focusOn2Africa()">🔍 Find 2Africa Cable</button>
            <button class="btn" onclick="focusOnSeacom()">🔍 Find SEACOM Cable</button>
            <button class="btn" onclick="showResults()">📊 Show Results</button>
        </div>
        
        <div id="results" style="display: none;">
            <div id="cable-summary" class="status"></div>
            <div id="cable-details" class="cable-list"></div>
        </div>
    </div>

    <div id="map"></div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize map with world bounds to prevent infinite horizontal scrolling
        const map = L.map('map', {
            worldCopyJump: false,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0
        }).setView([0, 20], 3);
        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        // Status elements
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');
        const summaryDiv = document.getElementById('cable-summary');
        const detailsDiv = document.getElementById('cable-details');

        // Data storage
        let allCables = [];
        let africanCables = [];
        let preservedCables = [];
        let filteredCables = [];

        // Copy exact filtering logic from main implementation
        const criticalAfricanCables = new Set([
            '2africa',
            'west-africa-cable-system-wacs',
            'africa-coast-to-europe-ace',
            'eastern-africa-submarine-system-eassy',
            'asia-africa-europe-1-aae-1',
            'safe',
            'sat-3wasc',
            'equiano',
            'africa-1',
            'seychelles-to-east-africa-system-seas',
            'the-east-african-marine-system-teams',
            'seacomtata-tgn-eurasia'
        ]);

        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    return (lng >= -180 && lng <= -30) && (lat >= -60 && lat <= 85);
                }

                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }

        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);
                    return inMainAsiaPacific || inPacificExtension;
                }

                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }

        function isAfricanCable(cable) {
            const name = cable.properties.name.toLowerCase();
            const id = cable.properties.id.toLowerCase();
            
            return name.includes('africa') || 
                   name.includes('wacs') || 
                   name.includes('ace') || 
                   name.includes('safe') || 
                   name.includes('eassy') ||
                   id.includes('africa') ||
                   id.includes('wacs') ||
                   id.includes('ace') ||
                   id.includes('safe') ||
                   id.includes('eassy') ||
                   criticalAfricanCables.has(id);
        }

        function updateStatus(message, type = 'info') {
            statusDiv.innerHTML = `<strong>Status:</strong> ${message}`;
            statusDiv.className = `status ${type}`;
        }

        function focusOnAfrica() {
            map.setView([0, 20], 3);
            updateStatus('Focused on Africa - Look for red cables (African) vs blue cables (others)');
        }

        function focusOn2Africa() {
            // Try to find 2Africa cable and focus on it
            const twoAfrica = preservedCables.find(c => c.properties.id === '2africa');
            if (twoAfrica && twoAfrica.geometry && twoAfrica.geometry.coordinates) {
                // Focus on a point along the 2Africa route (around South Africa)
                map.setView([-30, 25], 5);
                updateStatus('Focused on South Africa region - 2Africa cable should be visible in red', 'success');
            } else {
                updateStatus('2Africa cable not found in preserved cables!', 'error');
            }
        }

        function focusOnSeacom() {
            // Try to find SEACOM cable and focus on it
            const seacom = preservedCables.find(c => c.properties.id === 'seacomtata-tgn-eurasia');
            if (seacom && seacom.geometry && seacom.geometry.coordinates) {
                // Focus on East Africa region where SEACOM operates
                map.setView([-10, 40], 4);
                updateStatus('Focused on East Africa/Indian Ocean region - SEACOM/Tata TGN-Eurasia cable should be visible in red', 'success');
            } else {
                updateStatus('SEACOM/Tata TGN-Eurasia cable not found in preserved cables!', 'error');
            }
        }

        function showResults() {
            resultsDiv.style.display = 'block';
        }

        // Load and test cables
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                allCables = data.features;
                africanCables = data.features.filter(isAfricanCable);

                // Apply the exact same filtering logic as main implementation
                const filteredFeatures = data.features.filter(feature => {
                    // Always preserve critical African cables
                    if (criticalAfricanCables.has(feature.properties.id)) {
                        preservedCables.push(feature);
                        return true;
                    }

                    // Check if cable coordinates are in Americas or Asia-Pacific regions
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        const isAsiaPacific = isInAsiaPacificRegion(feature.geometry.coordinates);

                        if (isAmericas || isAsiaPacific) {
                            filteredCables.push(feature);
                            return false;
                        }
                        return true;
                    }
                    return true;
                });

                // Add cables to map
                filteredFeatures.forEach(feature => {
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAfrican = isAfricanCable(feature);
                        const isPreserved = criticalAfricanCables.has(feature.properties.id);
                        
                        let color = '#0066cc'; // Default blue
                        let weight = 1;
                        let opacity = 0.6;
                        
                        if (isPreserved) {
                            color = '#ff0000'; // Red for preserved African cables
                            weight = 3;
                            opacity = 0.9;
                        } else if (isAfrican) {
                            color = '#ff6600'; // Orange for other African cables
                            weight = 2;
                            opacity = 0.8;
                        }

                        const cable = L.geoJSON(feature, {
                            style: { color, weight, opacity }
                        });

                        cable.bindPopup(`
                            <strong>${feature.properties.name}</strong><br>
                            ID: ${feature.properties.id}<br>
                            Length: ${feature.properties.length || 'N/A'}<br>
                            RFS: ${feature.properties.rfs || 'N/A'}<br>
                            <strong>African Cable:</strong> ${isAfrican ? 'YES' : 'No'}<br>
                            <strong>Preserved:</strong> ${isPreserved ? 'YES' : 'No'}
                        `);

                        cable.addTo(map);
                    }
                });

                // Update status and results
                const africanVisible = africanCables.filter(c => 
                    filteredFeatures.includes(c) || criticalAfricanCables.has(c.properties.id)
                ).length;

                updateStatus(`SUCCESS: ${africanVisible}/${africanCables.length} African cables visible. Red = Preserved, Orange = Other African, Blue = Non-African`, 'success');

                // Update summary
                summaryDiv.innerHTML = `
                    <strong>Results Summary:</strong><br>
                    Total cables: ${allCables.length}<br>
                    African cables found: ${africanCables.length}<br>
                    African cables visible: ${africanVisible}<br>
                    Preserved by exception: ${preservedCables.length}<br>
                    Filtered out: ${filteredCables.length}
                `;
                summaryDiv.className = 'status success';

                // Update details
                let detailsHtml = '<h4>Preserved African Cables:</h4><ul>';
                preservedCables.forEach(cable => {
                    detailsHtml += `<li><strong>${cable.properties.name}</strong> (${cable.properties.id})</li>`;
                });
                detailsHtml += '</ul>';

                detailsHtml += '<h4>Other Visible African Cables:</h4><ul>';
                africanCables.forEach(cable => {
                    if (!criticalAfricanCables.has(cable.properties.id) && filteredFeatures.includes(cable)) {
                        detailsHtml += `<li>${cable.properties.name} (${cable.properties.id})</li>`;
                    }
                });
                detailsHtml += '</ul>';

                detailsDiv.innerHTML = detailsHtml;

                // Console output
                console.log('=== FINAL VERIFICATION RESULTS ===');
                console.log(`Total cables: ${allCables.length}`);
                console.log(`African cables: ${africanCables.length}`);
                console.log(`African cables visible: ${africanVisible}`);
                console.log(`Preserved cables:`, preservedCables.map(c => c.properties.name));
                console.log('2Africa cable preserved:', preservedCables.some(c => c.properties.id === '2africa'));
                console.log('SEACOM/Tata TGN-Eurasia cable preserved:', preservedCables.some(c => c.properties.id === 'seacomtata-tgn-eurasia'));
            })
            .catch(error => {
                updateStatus(`ERROR: ${error.message}`, 'error');
                console.error('Error:', error);
            });

        // Add legend
        const legend = L.control({position: 'bottomright'});
        legend.onAdd = function (map) {
            const div = L.DomUtil.create('div', 'info legend');
            div.innerHTML = `
                <div style="background: white; padding: 10px; border-radius: 5px; box-shadow: 0 0 15px rgba(0,0,0,0.2);">
                    <strong>Legend</strong><br>
                    <span style="color: #ff0000; font-weight: bold;">━━━</span> Preserved African Cables<br>
                    <span style="color: #ff6600; font-weight: bold;">━━━</span> Other African Cables<br>
                    <span style="color: #0066cc;">━━━</span> Non-African Cables
                </div>
            `;
            return div;
        };
        legend.addTo(map);
    </script>
</body>
</html>
