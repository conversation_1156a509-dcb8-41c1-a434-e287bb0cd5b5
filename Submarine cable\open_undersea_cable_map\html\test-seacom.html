<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEACOM Cable Test</title>
</head>
<body>
    <h1>SEACOM/Tata TGN-Eurasia Cable Test</h1>
    <div id="results"></div>

    <script>
        // Test if SEACOM cable is being filtered correctly
        async function testSeacomCable() {
            try {
                const response = await fetch('../cable/cable-geo.json');
                const data = await response.json();
                
                // Find SEACOM cable
                const seacomCable = data.features.find(feature => 
                    feature.properties.id === 'seacomtata-tgn-eurasia'
                );
                
                const resultsDiv = document.getElementById('results');
                
                if (seacomCable) {
                    resultsDiv.innerHTML += `<p><strong>✅ SEACOM Cable Found!</strong></p>`;
                    resultsDiv.innerHTML += `<p>Name: ${seacomCable.properties.name}</p>`;
                    resultsDiv.innerHTML += `<p>ID: ${seacomCable.properties.id}</p>`;
                    
                    // Test filtering logic
                    const criticalAfricanCables = new Set([
                        '2africa',
                        'west-africa-cable-system-wacs',
                        'africa-coast-to-europe-ace',
                        'eastern-africa-submarine-system-eassy',
                        'asia-africa-europe-1-aae-1',
                        'safe',
                        'sat-3wasc',
                        'equiano',
                        'africa-1',
                        'seychelles-to-east-africa-system-seas',
                        'the-east-african-marine-system-teams',
                        'seacomtata-tgn-eurasia'
                    ]);
                    
                    const isPreserved = criticalAfricanCables.has(seacomCable.properties.id);
                    
                    if (isPreserved) {
                        resultsDiv.innerHTML += `<p><strong>✅ Cable is in critical African cables list - WILL BE PRESERVED</strong></p>`;
                    } else {
                        resultsDiv.innerHTML += `<p><strong>❌ Cable is NOT in critical African cables list - WILL BE FILTERED</strong></p>`;
                    }
                    
                    // Check coordinates
                    if (seacomCable.geometry && seacomCable.geometry.coordinates) {
                        resultsDiv.innerHTML += `<p>Cable has geometry coordinates: Yes</p>`;
                        
                        // Test Asia-Pacific filtering
                        function isInAsiaPacificRegion(coordinates) {
                            if (!coordinates || !Array.isArray(coordinates)) return false;

                            function checkCoordinates(coords) {
                                if (!Array.isArray(coords)) return false;

                                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                                    const lng = coords[0];
                                    const lat = coords[1];

                                    const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

                                    return inMainAsiaPacific || inPacificExtension;
                                }

                                for (let item of coords) {
                                    if (checkCoordinates(item)) {
                                        return true;
                                    }
                                }

                                return false;
                            }

                            return checkCoordinates(coordinates);
                        }
                        
                        const wouldBeFilteredByAsiaPacific = isInAsiaPacificRegion(seacomCable.geometry.coordinates);
                        
                        if (wouldBeFilteredByAsiaPacific) {
                            resultsDiv.innerHTML += `<p><strong>⚠️ Cable has coordinates in Asia-Pacific region (would be filtered without preservation)</strong></p>`;
                        } else {
                            resultsDiv.innerHTML += `<p>Cable does not have coordinates in Asia-Pacific region</p>`;
                        }
                    }
                    
                } else {
                    resultsDiv.innerHTML += `<p><strong>❌ SEACOM Cable NOT Found in cable-geo.json!</strong></p>`;
                }
                
                // Count total cables
                resultsDiv.innerHTML += `<p>Total cables in dataset: ${data.features.length}</p>`;
                
            } catch (error) {
                document.getElementById('results').innerHTML = `<p><strong>❌ Error: ${error.message}</strong></p>`;
            }
        }
        
        // Run test when page loads
        testSeacomCable();
    </script>
</body>
</html>
