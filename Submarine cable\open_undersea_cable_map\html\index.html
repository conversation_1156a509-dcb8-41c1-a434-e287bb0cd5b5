<!DOCTYPE html>
<html>
<head>
    <title>Submarine Cable Map</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        
        body { margin: 0; padding: 0; }
        #map { 
            height: 100vh;
            width: 100%;
        }
        .info {
            padding: 6px 8px;
            font: 14px/16px Arial, Helvetica, sans-serif;
            background: white;
            background: rgba(255,255,255,0.8);
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
            border-radius: 5px;
        }
        .info h4 {
            margin: 0 0 5px;
            color: #777;
        }

        /* Interactive cable label styles - professional design */
        .interactive-cable-label {
            background: none !important;
            border: none !important;
            box-shadow: none !important;
        }

        .interactive-cable-label-text {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(52, 73, 94, 0.3);
            border-radius: 6px;
            padding: 8px 12px;
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON>l, sans-serif;
            font-size: 13px;
            font-weight: 600;
            color: #2c3e50;
            text-align: center;
            white-space: nowrap;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
            pointer-events: none;
            user-select: none;
            position: relative;
            line-height: 1.2;
        }

        .interactive-cable-label-text::before {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid rgba(255, 255, 255, 0.95);
        }

        /* Responsive design for interactive labels */
        @media (max-width: 768px) {
            .interactive-cable-label-text {
                font-size: 12px;
                padding: 6px 10px;
            }
        }

        /* Professional hover cursor for cables */
        .leaflet-interactive {
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize the map with world bounds to prevent infinite horizontal scrolling

        const map = L.map('map', {
            worldCopyJump: false,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0
        }).setView([20, 0], 2);
        
        // Add CartoDB Positron tile layer (clean, well-labeled)
        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        // Create layer groups
        const cableLayer = L.layerGroup().addTo(map);
        const landingPointLayer = L.layerGroup().addTo(map);

        // Professional color palette inspired by submarinecablemap.com
        // Muted, professional colors that are easy on the eyes and provide good contrast
        const professionalCableColors = [
            '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6C757D', '#495057',
            '#4A90A4', '#8E44AD', '#E67E22', '#D35400', '#7F8C8D', '#2C3E50',
            '#3498DB', '#9B59B6', '#F39C12', '#E74C3C', '#95A5A6', '#34495E',
            '#5DADE2', '#BB8FCE', '#F7DC6F', '#F1948A', '#AEB6BF', '#566573',
            '#85C1E9', '#D2B4DE', '#FCF3CF', '#FADBD8', '#D5DBDB', '#85929E',
            '#2874A6', '#7D3C98', '#B7950B', '#CB4335', '#616A6B', '#283747',
            '#1B4F72', '#633974', '#9A7D0A', '#A93226', '#515A5A', '#1C2833',
            '#154360', '#512E5F', '#7E5109', '#922B21', '#424949', '#17202A',
            '#5499C7', '#AF7AC5', '#F4D03F', '#EC7063', '#A6ACAF', '#5D6D7E',
            '#3F51B5', '#8E24AA', '#FF9800', '#F44336', '#9E9E9E', '#607D8B',
            '#1976D2', '#7B1FA2', '#FF5722', '#E91E63', '#757575', '#455A64',
            '#0277BD', '#6A1B9A', '#FF6F00', '#C2185B', '#616161', '#37474F'
        ];

        // Function to get professional color for each cable
        function getProfessionalCableColor(cableId, index) {
            // Create a hash from the cable ID for consistent color assignment
            let hash = 0;
            for (let i = 0; i < cableId.length; i++) {
                const char = cableId.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }

            // Combine hash with index for better distribution
            const colorIndex = (Math.abs(hash) + index) % professionalCableColors.length;
            return professionalCableColors[colorIndex];
        }

        // Interactive cable label management
        let currentCableLabel = null;
        let currentHighlightedCable = null;

        // Function to create interactive cable label on click
        function createInteractiveCableLabel(feature, clickLatLng) {
            // Remove existing label if any
            if (currentCableLabel) {
                map.removeLayer(currentCableLabel);
                currentCableLabel = null;
            }

            const cableName = feature.properties.name || feature.properties.id;
            if (!cableName) return;

            // Create professional label styling
            const labelIcon = L.divIcon({
                className: 'interactive-cable-label',
                html: `<div class="interactive-cable-label-text">${cableName}</div>`,
                iconSize: [null, null], // Auto-size based on content
                iconAnchor: [0, 0]
            });

            // Position label at click location or cable midpoint
            let labelPosition = clickLatLng;

            // If no click position provided, use cable midpoint
            if (!labelPosition && feature.geometry && feature.geometry.coordinates) {
                let coordinates = feature.geometry.coordinates;

                if (feature.geometry.type === 'MultiLineString') {
                    // Find the longest segment for label placement
                    let longestSegment = coordinates[0];
                    let maxLength = 0;

                    coordinates.forEach(segment => {
                        if (segment.length > maxLength) {
                            maxLength = segment.length;
                            longestSegment = segment;
                        }
                    });
                    coordinates = longestSegment;
                }

                if (coordinates.length >= 2) {
                    const midIndex = Math.floor(coordinates.length / 2);
                    const midPoint = coordinates[midIndex];
                    if (midPoint && midPoint.length >= 2) {
                        labelPosition = L.latLng(midPoint[1], midPoint[0]);
                    }
                }
            }

            if (labelPosition) {
                currentCableLabel = L.marker(labelPosition, {
                    icon: labelIcon,
                    interactive: false,
                    zIndexOffset: 2000
                }).addTo(map);
            }
        }

        // Function to remove cable label when clicking elsewhere
        function removeCableLabel() {
            if (currentCableLabel) {
                map.removeLayer(currentCableLabel);
                currentCableLabel = null;
            }

            // Reset cable highlighting
            if (currentHighlightedCable) {
                currentHighlightedCable.setStyle({
                    weight: 2,
                    opacity: 0.8
                });
                currentHighlightedCable = null;
            }
        }

        // Add info control
        const info = L.control();
        info.onAdd = function(map) {
            this._div = L.DomUtil.create('div', 'info');
            this.update();
            return this._div;
        };
        info.update = function(props) {
            this._div.innerHTML = '<h4>Submarine Cable Map</h4>' +
                '<div style="color: #d63384; font-size: 12px; margin-bottom: 8px;">⚠️ Americas and Asia-Pacific cables hidden • African infrastructure preserved</div>' +
                (props ?
                    '<b>' + props.name + '</b><br/>' +
                    (props.rfs ? 'Ready for Service: ' + props.rfs + '<br/>' : '') +
                    (props.length ? 'Length: ' + props.length + ' km<br/>' : '') +
                    (props.owners ? 'Owners: ' + props.owners + '<br/>' : '')
                    : 'Hover over a cable');
        };
        info.addTo(map);

        // Define Americas countries (North, Central, and South America)
        const americasCountries = new Set([
            // North America
            'United States', 'Canada', 'Mexico', 'Greenland',
            // Central America and Caribbean
            'Guatemala', 'Belize', 'El Salvador', 'Honduras', 'Nicaragua', 'Costa Rica', 'Panama',
            'Cuba', 'Jamaica', 'Haiti', 'Dominican Republic', 'Puerto Rico', 'Bahamas', 'Barbados',
            'Trinidad and Tobago', 'Grenada', 'Saint Vincent and the Grenadines', 'Saint Lucia',
            'Dominica', 'Antigua and Barbuda', 'Saint Kitts and Nevis', 'Martinique', 'Guadeloupe',
            'Saint Barthélemy', 'Saint Martin', 'Sint Maarten', 'Anguilla', 'British Virgin Islands',
            'Virgin Islands (U.S.)', 'Virgin Islands (U.K.)', 'Cayman Islands', 'Turks and Caicos Islands',
            'Aruba', 'Curaçao', 'Bonaire, Sint Eustatius and Saba', 'Netherlands', 'French Guiana',
            // South America
            'Brazil', 'Argentina', 'Chile', 'Peru', 'Colombia', 'Venezuela', 'Ecuador', 'Bolivia',
            'Paraguay', 'Uruguay', 'Guyana', 'Suriname', 'French Guiana'
        ]);

        // Define Asia-Pacific countries and territories
        const asiaPacificCountries = new Set([
            // East Asia
            'China', 'Japan', 'South Korea', 'North Korea', 'Taiwan', 'Hong Kong', 'Macau', 'Mongolia',
            // Southeast Asia
            'Singapore', 'Indonesia', 'Philippines', 'Malaysia', 'Vietnam', 'Thailand', 'Myanmar',
            'Cambodia', 'Laos', 'Brunei', 'Timor-Leste',
            // South Asia
            'India', 'Pakistan', 'Bangladesh', 'Sri Lanka', 'Nepal', 'Bhutan', 'Maldives', 'Afghanistan',
            // Oceania and Pacific Islands
            'Australia', 'New Zealand', 'Papua New Guinea', 'Fiji', 'Solomon Islands', 'Vanuatu',
            'New Caledonia', 'Samoa', 'Tonga', 'Kiribati', 'Tuvalu', 'Nauru', 'Palau', 'Marshall Islands',
            'Micronesia', 'Cook Islands', 'French Polynesia', 'Wallis and Futuna', 'American Samoa',
            'Guam', 'Northern Mariana Islands', 'Cocos (Keeling) Islands', 'Christmas Island'
        ]);

        // Function to check if a cable has landing points in the Americas
        function isAmericasCable(cableId) {
            // We'll need to fetch individual cable data to check landing points
            // For now, we'll use a simpler approach based on cable coordinates
            return false; // Will be implemented below
        }

        // Function to check if coordinates are in Americas region (rough approximation)
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    // Americas longitude range (including Alaska and eastern Brazil)
                    // Expanded range to be more inclusive: -180° to -25°
                    return lng >= -180 && lng <= -25;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Function to check if coordinates are in Asia-Pacific region
        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];

                    // Asia-Pacific region boundaries (adjusted to exclude East Africa and Middle East):
                    // Main Asia-Pacific: Longitude 65°E to 180°E, Latitude -50°S to 80°N
                    // Pacific extension: Longitude -180°E to -120°W (for Pacific islands), Latitude -50°S to 80°N
                    const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

                    return inMainAsiaPacific || inPacificExtension;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Fetch and display cable routes
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total cables before filtering: ${data.features.length}`);

                // Critical African cables that should always be preserved
                const criticalAfricanCables = new Set([
                    '2africa',
                    'west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'asia-africa-europe-1-aae-1',
                    'safe',
                    'sat-3wasc',
                    'equiano',
                    'africa-1',
                    'seychelles-to-east-africa-system-seas',
                    'the-east-african-marine-system-teams',
                    'seacomtata-tgn-eurasia'
                ]);

                // Filter out cables in Americas and Asia-Pacific regions, but preserve critical African infrastructure
                const filteredFeatures = data.features.filter(feature => {
                    // Always preserve critical African cables
                    if (criticalAfricanCables.has(feature.properties.id)) {
                        console.log(`Preserving critical African cable: ${feature.properties.name}`);
                        return true;
                    }

                    // Check if cable coordinates are in Americas or Asia-Pacific regions
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        const isAsiaPacific = isInAsiaPacificRegion(feature.geometry.coordinates);

                        if (isAmericas) {
                            console.log(`Filtering out Americas cable: ${feature.properties.name}`);
                            return false;
                        }
                        if (isAsiaPacific) {
                            console.log(`Filtering out Asia-Pacific cable: ${feature.properties.name}`);
                            return false;
                        }
                        return true; // Keep cable if it's not in filtered regions
                    }
                    return true; // Keep cable if we can't determine location
                });

                console.log(`Total cables after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                // Create cables with professional colors and interactive labeling
                L.geoJSON(filteredData, {
                    style: function(feature) {
                        // Get professional color for this cable
                        const cableIndex = filteredData.features.indexOf(feature);
                        const professionalColor = getProfessionalCableColor(feature.properties.id, cableIndex);

                        return {
                            color: professionalColor,
                            weight: 2,
                            opacity: 0.8,
                            lineCap: 'round',
                            lineJoin: 'round',
                            // Use dashed line for planned cables
                            dashArray: feature.properties.is_planned ? '8, 8' : null
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        // Store original style for consistent behavior
                        const cableIndex = filteredData.features.indexOf(feature);
                        const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);

                        layer.on({
                            mouseover: function(e) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1,
                                    color: originalColor
                                });
                                info.update(feature.properties);
                            },
                            mouseout: function(e) {
                                const layer = e.target;
                                // Only reset if this isn't the currently highlighted cable
                                if (currentHighlightedCable !== layer) {
                                    layer.setStyle({
                                        weight: 2,
                                        opacity: 0.8,
                                        color: originalColor
                                    });
                                }
                                info.update();
                            },
                            click: function(e) {
                                // Remove previous label and highlighting
                                removeCableLabel();

                                // Highlight the clicked cable
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1,
                                    color: originalColor
                                });
                                currentHighlightedCable = layer;

                                // Create interactive label at click position
                                createInteractiveCableLabel(feature, e.latlng);

                                // Prevent event bubbling to map
                                L.DomEvent.stopPropagation(e);
                            }
                        });

                        // Enhanced popup with professional styling
                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<b style="color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</b><br/>';
                        popupContent += '<span style="color: ' + originalColor + '; font-size: 16px; margin-right: 5px;">●</span>';
                        popupContent += '<span style="color: #7f8c8d; font-size: 12px;">Cable Color</span><br/>';

                        if (feature.properties.rfs) {
                            popupContent += '<span style="color: #34495e; font-size: 12px;">Ready for Service: ' + feature.properties.rfs + '</span><br/>';
                        }
                        if (feature.properties.length) {
                            popupContent += '<span style="color: #34495e; font-size: 12px;">Length: ' + feature.properties.length + ' km</span><br/>';
                        }
                        if (feature.properties.owners) {
                            popupContent += '<span style="color: #34495e; font-size: 12px;">Owners: ' + feature.properties.owners + '</span>';
                        }
                        popupContent += '<br/><em style="color: #95a5a6; font-size: 11px;">Click cable to show name label</em>';
                        popupContent += '</div>';

                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);
            });

        // Function to check if landing point coordinates are in Americas region
        function isLandingPointInAmericas(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            // Americas longitude range (including Alaska and eastern Brazil)
            // Expanded range to be more inclusive: -180° to -25°
            return lng >= -180 && lng <= -25;
        }

        // Function to check if landing point coordinates are in Asia-Pacific region
        function isLandingPointInAsiaPacific(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            const lat = coordinates[1];

            // Asia-Pacific region boundaries (adjusted to exclude East Africa and Middle East):
            // Main Asia-Pacific: Longitude 65°E to 180°E, Latitude -50°S to 80°N
            // Pacific extension: Longitude -180°E to -120°W (for Pacific islands), Latitude -50°S to 80°N
            const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
            const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

            return inMainAsiaPacific || inPacificExtension;
        }

        // Fetch and display landing points
        fetch('../landing-point/landing-point-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total landing points before filtering: ${data.features.length}`);

                // Critical African landing points that should always be preserved
                const criticalAfricanLandingPoints = new Set([
                    'cape-town-south-africa',
                    'mtunzini-south-africa',
                    'port-elizabeth-south-africa',
                    'gqeberha-south-africa',
                    'mombasa-kenya',
                    'dar-es-salaam-tanzania',
                    'djibouti-city-djibouti',
                    'lagos-nigeria',
                    'accra-ghana',
                    'dakar-senegal',
                    'casablanca-morocco',
                    'alexandria-egypt',
                    'port-said-egypt',
                    'zafarana-egypt',
                    'mumbai-india',
                    'maputo-mozambique',
                    'jeddah-saudi-arabia'
                ]);

                // Filter out landing points in Americas and Asia-Pacific regions, but preserve critical African infrastructure
                const filteredFeatures = data.features.filter(feature => {
                    // Always preserve critical African landing points
                    if (criticalAfricanLandingPoints.has(feature.properties.id)) {
                        console.log(`Preserving critical African landing point: ${feature.properties.name}`);
                        return true;
                    }

                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isLandingPointInAmericas(feature.geometry.coordinates);
                        const isAsiaPacific = isLandingPointInAsiaPacific(feature.geometry.coordinates);

                        if (isAmericas) {
                            console.log(`Filtering out Americas landing point: ${feature.properties.name}`);
                            return false;
                        }
                        if (isAsiaPacific) {
                            console.log(`Filtering out Asia-Pacific landing point: ${feature.properties.name}`);
                            return false;
                        }
                        return true; // Keep landing point if it's not in filtered regions
                    }
                    return true; // Keep landing point if we can't determine location
                });

                console.log(`Total landing points after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                L.geoJSON(filteredData, {
                    pointToLayer: (feature, latlng) => {
                        return L.circleMarker(latlng, {
                            radius: 5,
                            fillColor: '#FF0000',
                            color: '#000',
                            weight: 1,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: (feature, layer) => {
                        if (feature.properties.name) {
                            layer.bindPopup(`<b>${feature.properties.name}</b><br>
                                ${feature.properties.country || ''}`);
                        }
                    }
                }).addTo(landingPointLayer);
            });

        // Add layer control with improved tile options
        const baseMaps = {
            "CartoDB Positron": L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "Esri World Street": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, DeLorme, NAVTEQ, USGS, Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong), Esri (Thailand), TomTom, 2012',
                maxZoom: 19
            }),
            "CartoDB Voyager": L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 19
            }),
            "Satellite": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                maxZoom: 19
            })
        };

        const overlayMaps = {
            "Submarine Cables": cableLayer,
            "Landing Points": landingPointLayer
        };

        L.control.layers(baseMaps, overlayMaps).addTo(map);

        // Add scale control
        L.control.scale().addTo(map);

        // Add click handler to map for removing labels when clicking on empty areas
        map.on('click', function(e) {
            // Only remove label if click wasn't on a cable (handled by cable click event)
            removeCableLabel();
        });
    </script>
</body>
</html>
